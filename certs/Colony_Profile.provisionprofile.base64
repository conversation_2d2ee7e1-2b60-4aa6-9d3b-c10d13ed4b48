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