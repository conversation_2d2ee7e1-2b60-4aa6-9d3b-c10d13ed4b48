<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Required for App Store distribution -->
    <key>com.apple.security.app-sandbox</key>
    <true/>
    
    <!-- Network access for Autonomi network communication -->
    <key>com.apple.security.network.client</key>
    <true/>
    
    <!-- File system access for downloads and file management -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    
    <!-- Downloads folder access -->
    <key>com.apple.security.files.downloads.read-write</key>
    <true/>
    
    <!-- Allow opening files with external applications -->
    <key>com.apple.security.files.user-selected.executable</key>
    <true/>
    
    <!-- Required for hardened runtime -->
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    
    <!-- Allow unsigned executable memory for WebKit -->
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    
    <!-- Disable library validation for third-party libraries -->
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    
    <!-- Allow DYLD environment variables -->
    <key>com.apple.security.cs.allow-dyld-environment-variables</key>
    <true/>
    
    <!-- Microphone access (if needed for future features) -->
    <key>com.apple.security.device.microphone</key>
    <false/>
    
    <!-- Camera access (if needed for future features) -->
    <key>com.apple.security.device.camera</key>
    <false/>
    
    <!-- Location access (if needed for future features) -->
    <key>com.apple.security.personal-information.location</key>
    <false/>
    
    <!-- Contacts access (if needed for future features) -->
    <key>com.apple.security.personal-information.addressbook</key>
    <false/>
    
    <!-- Calendar access (if needed for future features) -->
    <key>com.apple.security.personal-information.calendars</key>
    <false/>
</dict>
</plist>
