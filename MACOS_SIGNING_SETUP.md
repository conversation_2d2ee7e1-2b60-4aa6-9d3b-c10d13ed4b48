# macOS Code Signing and App Store Connect Setup

This document explains how to set up automated macOS code signing, notarization, and App Store Connect integration for the Colony application.

## Prerequisites

1. **Apple Developer Account**: You need a paid Apple Developer account ($99/year)
2. **App Store Connect Access**: Access to App Store Connect for your developer account
3. **Xcode Command Line Tools**: Installed on your development machine for certificate generation

## Step 1: Generate Certificates

**Important Note**: If you're using OpenSSL 3.x, you must include the `-legacy` flag when creating P12 files to ensure compatibility with macOS Keychain. This is already included in the commands below.

### 1.1 Developer ID Application Certificate (for distribution outside App Store)

```bash
# Generate a certificate signing request
openssl req -new -newkey rsa:2048 -nodes -keyout developer_id.key -out developer_id.csr

# Upload the CSR to Apple Developer Portal:
# 1. Go to https://developer.apple.com/account/resources/certificates/list
# 2. Click "+" to create a new certificate
# 3. Select "Developer ID Application"
# 4. Upload your CSR file
# 5. Download the certificate (.cer file)

# Convert to P12 format
openssl x509 -in developer_id.cer -inform DER -out developer_id.crt
openssl pkcs12 -export -out developer_id.p12 -inkey developer_id.key -in developer_id.crt -legacy
```

### 1.2 Mac App Store Distribution Certificate (for App Store distribution)

```bash
# Generate a certificate signing request
openssl req -new -newkey rsa:2048 -nodes -keyout mac_app_store.key -out mac_app_store.csr

# Upload the CSR to Apple Developer Portal:
# 1. Go to https://developer.apple.com/account/resources/certificates/list
# 2. Click "+" to create a new certificate
# 3. Select "Mac App Store Distribution"
# 4. Upload your CSR file
# 5. Download the certificate (.cer file)

# Convert to P12 format
openssl x509 -in mac_app_store.cer -inform DER -out mac_app_store.crt
openssl pkcs12 -export -out mac_app_store.p12 -inkey mac_app_store.key -in mac_app_store.crt -legacy
```

### 1.3 Mac Installer Distribution Certificate

```bash
# Generate a certificate signing request
openssl req -new -newkey rsa:2048 -nodes -keyout mac_installer.key -out mac_installer.csr

# Upload the CSR to Apple Developer Portal:
# 1. Go to https://developer.apple.com/account/resources/certificates/list
# 2. Click "+" to create a new certificate
# 3. Select "Mac Installer Distribution"
# 4. Upload your CSR file
# 5. Download the certificate (.cer file)

# Convert to P12 format
openssl x509 -in mac_installer.cer -inform DER -out mac_installer.crt
openssl pkcs12 -export -out mac_installer.p12 -inkey mac_installer.key -in mac_installer.crt -legacy
```

## Step 2: Create App Store Connect API Key

1. Go to [App Store Connect](https://appstoreconnect.apple.com/)
2. Navigate to "Users and Access" > "Keys"
3. Click "+" to create a new API key
4. Give it a name (e.g., "Colony GitHub Actions")
5. Select "Developer" role
6. Download the `.p8` file
7. Note the Key ID and Issuer ID
   - Issuer ID: 98d02a1b-3a3f-4177-8828-5c7f3962cc71
   - KEY ID: FN8TT333RB

## Step 3: Create Provisioning Profile (for App Store distribution)

1. Go to [Apple Developer Portal](https://developer.apple.com/account/resources/profiles/list)
2. Click "+" to create a new profile
3. Select "Mac App Store" under Distribution
4. Select your App ID (com.colony.gui)
5. Select your Mac App Store Distribution certificate
6. Download the provisioning profile (.mobileprovision file)

## Step 4: Configure GitHub Secrets

Add the following secrets to your GitHub repository (Settings > Secrets and variables > Actions):

### Required Secrets:

```
APPLE_CERTIFICATE
# Base64 encoded Developer ID Application certificate (.p12 file)
# Generate with: base64 -i developer_id.p12 | pbcopy

APPLE_CERTIFICATE_PASSWORD
# Password used when creating the .p12 file

APPLE_INSTALLER_CERTIFICATE
# Base64 encoded Mac Installer Distribution certificate (.p12 file)
# Generate with: base64 -i mac_installer.p12 | pbcopy

APPLE_INSTALLER_CERTIFICATE_PASSWORD
# Password used when creating the installer .p12 file

APPLE_PROVISIONING_PROFILE
# Base64 encoded provisioning profile (.mobileprovision file)
# Generate with: base64 -i profile.mobileprovision | pbcopy

APPLE_SIGNING_IDENTITY --> Developer ID Application: Charles McClish (3364NM68HH)
# The name of your signing identity (e.g., "Developer ID Application: Your Name (TEAM_ID)")
# Find with: security find-identity -v -p codesigning

APPLE_ID --> <EMAIL>
# Your Apple ID email address

APPLE_PASSWORD --> mrzm-sojz-jhho-kewc
# App-specific password for your Apple ID
# Generate at: https://appleid.apple.com/account/manage > Sign-In and Security > App-Specific Passwords

APPLE_TEAM_ID --> 3364NM68HH
# Your Apple Developer Team ID (10-character string)
# Find in Apple Developer Portal > Membership

APP_STORE_CONNECT_API_KEY
# Base64 encoded App Store Connect API key (.p8 file)
# Generate with: base64 -i AuthKey_XXXXXXXXXX.p8 | pbcopy

APP_STORE_CONNECT_API_KEY_ID --> FN8TT333RB
# The Key ID from App Store Connect (10-character string)

APP_STORE_CONNECT_ISSUER_ID --> 98d02a1b-3a3f-4177-8828-5c7f3962cc71
# The Issuer ID from App Store Connect (UUID format)
```

## Step 5: Update App Information

### 5.1 Register App in App Store Connect

1. Go to [App Store Connect](https://appstoreconnect.apple.com/)
2. Click "My Apps" > "+"
3. Select "New App"
4. Choose "macOS" platform
5. Enter app information:
   - Name: Colony
   - Primary Language: English
   - Bundle ID: com.colony.gui
   - SKU: com.colony.gui

### 5.2 Update App Metadata

Fill in the required metadata in App Store Connect:
- App description
- Keywords
- Screenshots (required: 1280x800, 1440x900, 2560x1600, 2880x1800)
- App icon (1024x1024)
- Privacy policy URL
- Support URL

## Step 6: Build and Release Process

### Automatic Process

The GitHub workflow will automatically:

1. **Build for both architectures**: aarch64 (Apple Silicon) and x86_64 (Intel)
2. **Code sign**: Using your Developer ID Application certificate
3. **Notarize**: Submit to Apple for notarization
4. **Staple**: Attach notarization ticket to the app
5. **Create DMG**: Generate signed and notarized DMG files
6. **Upload to GitHub**: Release artifacts to GitHub Releases

### Manual App Store Upload (Optional)

To upload to App Store Connect, uncomment the upload section in the workflow or run manually:

```bash
# Upload to App Store Connect
xcrun altool --upload-app \
  --type osx \
  --file "path/to/Colony.app" \
  --apiKey YOUR_API_KEY_ID \
  --apiIssuer YOUR_ISSUER_ID \
  --verbose
```

## Troubleshooting

### Common Issues:

1. **Certificate not found**: Ensure certificates are properly imported and keychain is unlocked
2. **Notarization fails**: Check that hardened runtime is enabled and entitlements are correct
3. **App Store upload fails**: Verify app metadata is complete in App Store Connect
4. **Signing identity not found**: Check that APPLE_SIGNING_IDENTITY matches exactly
5. **MAC verification failed during PKCS12 import**: This occurs with OpenSSL 3.x. Recreate your P12 files with the `-legacy` flag:
   ```bash
   openssl pkcs12 -export -out certificate.p12 -inkey private.key -in certificate.crt -legacy
   ```

### Verification Commands:

```bash
# List available signing identities
security find-identity -v -p codesigning

# Verify code signature
codesign -v -v --deep --strict /path/to/Colony.app

# Check notarization status
xcrun stapler validate /path/to/Colony.app

# Test Gatekeeper
spctl -a -v /path/to/Colony.app
```

## Architecture Support

The workflow now builds for both:
- **aarch64-apple-darwin**: Apple Silicon Macs (M1, M2, M3, etc.)
- **x86_64-apple-darwin**: Intel-based Macs

Both architectures will be signed, notarized, and released as separate DMG files.
